# TeamSphere Multi-Tenant Development Prompt

## Context
You are working on transforming the existing TeamSphere time-sheet portal into a multi-tenant SaaS platform. The current system is a single-tenant Spring Boot + Angular application with hardcoded fields and workflows.

## Current Architecture
- **Backend**: Spring Boot with PostgreSQL, JWT authentication
- **Frontend**: Angular 16+ with Angular Material
- **Database**: PostgreSQL with existing tables (time_entries, projects, users, employee)
- **Key Models**: TimeEntry, Project, User, Employee, UserProject

## Goal
Implement a multi-tenant system where:
1. Each client (tenant) has isolated data
2. Customizable time entry fields per tenant
3. Configurable workflows and business rules
4. Dynamic form generation based on tenant configuration
5. Tenant-specific activities, processes, and attendance types

## Implementation Tasks

### Phase 1: Backend Multi-Tenant Foundation

#### Task 1: Database Schema Implementation
- [x] Execute `database/multi-tenant-schema.sql`
- [x] Execute `database/tenant-migration.sql`
- [ ] Verify all existing data is migrated to default tenant

#### Task 2: Core Tenant Models
Implement these models in `backend/capsAllocation/src/main/java/com/vbs/capsAllocation/model/`:

1. **Tenant.java** - Core tenant entity
2. **TenantStatus.java** - Enum for tenant status
3. **TenantConfiguration.java** - Key-value configuration store
4. **ConfigType.java** - Enum for configuration value types
5. **CustomField.java** - Dynamic field definitions
6. **EntityType.java** - Enum for entity types (TIME_ENTRY, PROJECT, etc.)
7. **FieldType.java** - Enum for field types (TEXT, SELECT, etc.)

#### Task 3: Tenant Context Management
Implement these services:

1. **TenantContextService.java** - ThreadLocal tenant context
2. **TenantResolutionFilter.java** - Resolve tenant from request
3. **TenantRepository.java** - Tenant data access
4. **TenantConfigurationRepository.java** - Configuration data access

#### Task 4: Update Existing Models
Modify existing models to be tenant-aware:

1. Add `@ManyToOne Tenant tenant` to:
   - TimeEntry
   - Project
   - User
   - Employee
   - UserProject

2. Add `@Column(columnDefinition = "TEXT") String customData` for JSON custom fields

#### Task 5: Tenant Service Layer
Create `TenantService.java` with methods:
- `createTenant(CreateTenantDTO dto)`
- `getTenantConfiguration(Long tenantId)`
- `updateTenantConfiguration(Long tenantId, Map<String, Object> config)`
- `getTenantCustomFields(Long tenantId, EntityType entityType)`

### Phase 2: Dynamic Configuration System

#### Task 6: Configuration Management
Create configuration services:

1. **TenantConfigurationService.java**
   - `getConfigValue(String category, String key, Class<T> type)`
   - `setConfigValue(String category, String key, Object value)`
   - `getConfigurationsByCategory(String category)`

2. **CustomFieldService.java**
   - `getCustomFields(EntityType entityType)`
   - `createCustomField(CreateCustomFieldDTO dto)`
   - `validateCustomFieldValue(Long fieldId, String value)`

#### Task 7: Dynamic Form Engine
Create form generation services:

1. **FormConfigurationService.java**
   - `generateFormConfig(EntityType entityType)`
   - `validateFormData(EntityType entityType, Map<String, Object> data)`

2. **BusinessRuleEngine.java**
   - `evaluateRules(EntityType entityType, Object entity)`
   - `executeActions(List<RuleAction> actions, Object entity)`

### Phase 3: API Layer Updates

#### Task 8: Tenant-Aware Controllers
Update existing controllers to be tenant-aware:

1. **TimeEntryController.java**
   - Add tenant filtering to all queries
   - Include custom field data in responses
   - Validate custom fields on create/update

2. **ProjectController.java**
   - Add tenant-specific project management
   - Include custom field support

3. **New TenantController.java**
   - Tenant CRUD operations
   - Configuration management endpoints
   - Custom field management

#### Task 9: Enhanced DTOs
Create/update DTOs:

1. **TenantDTO.java** - Tenant information
2. **TenantConfigurationDTO.java** - Configuration data
3. **CustomFieldDTO.java** - Custom field definitions
4. **DynamicFormDTO.java** - Form configuration
5. Update existing DTOs to include `customData` field

### Phase 4: Frontend Multi-Tenant Support

#### Task 10: Tenant Context Service
Create Angular services:

1. **TenantContextService** - Manage current tenant context
2. **TenantConfigurationService** - Load tenant configurations
3. **CustomFieldService** - Manage custom field definitions

#### Task 11: Dynamic Form Components
Create Angular components:

1. **DynamicFormComponent** - Render forms based on configuration
2. **DynamicFieldComponent** - Individual field renderer
3. **CustomFieldManagerComponent** - Admin interface for custom fields

#### Task 12: Configuration-Driven UI
Update existing components:

1. **TimeEntryFormComponent** - Use dynamic form generation
2. **ProjectFormComponent** - Include custom fields
3. **TimeEntryComponent** - Display custom columns

### Phase 5: Workflow Engine

#### Task 13: Workflow Models
Create workflow entities:

1. **ApprovalWorkflow.java** - Workflow definitions
2. **BusinessRule.java** - Business rule definitions
3. **WorkflowStep.java** - Individual workflow steps

#### Task 14: Workflow Engine
Implement workflow processing:

1. **WorkflowEngine.java** - Execute workflows
2. **ApprovalService.java** - Handle approvals
3. **NotificationService.java** - Send notifications

## Key Design Patterns to Use

### 1. Strategy Pattern for Business Rules
```java
public interface BusinessRule<T> {
    boolean evaluate(T entity, TenantConfiguration config);
    String getRuleName();
}
```

### 2. Factory Pattern for Dynamic Forms
```java
public interface FormFieldFactory {
    FormField createField(CustomField customField);
}
```

### 3. Template Method for Workflows
```java
public abstract class WorkflowProcessor {
    public final void processWorkflow(WorkflowContext context) {
        validateInput(context);
        executeSteps(context);
        notifyStakeholders(context);
    }
}
```

## Configuration Examples

### Sample Tenant Configuration
```json
{
  "TIME_ENTRY": {
    "MAX_DAILY_HOURS": 8,
    "OVERTIME_THRESHOLD": 8,
    "ALLOW_WEEKEND_ENTRIES": false
  },
  "WORKFLOW": {
    "APPROVAL_LEVELS": 2,
    "AUTO_APPROVAL_THRESHOLD": 480
  },
  "UI": {
    "THEME_COLOR": "#1976d2",
    "COMPANY_NAME": "Client Company"
  }
}
```

### Sample Custom Field
```json
{
  "fieldName": "cost_center",
  "fieldLabel": "Cost Center",
  "fieldType": "SELECT",
  "isRequired": true,
  "fieldOptions": [
    {"value": "CC001", "label": "Development"},
    {"value": "CC002", "label": "QA"}
  ]
}
```

## Testing Strategy

### Unit Tests
- Test tenant isolation
- Test configuration loading
- Test custom field validation
- Test workflow execution

### Integration Tests
- Test tenant resolution
- Test multi-tenant data access
- Test API endpoints with different tenants

### End-to-End Tests
- Test complete tenant onboarding flow
- Test custom field creation and usage
- Test workflow approval process

## Security Considerations

1. **Tenant Isolation**: Ensure complete data separation
2. **Row-Level Security**: Implement database-level isolation
3. **API Security**: Validate tenant access in all endpoints
4. **Configuration Security**: Encrypt sensitive configuration values

## Performance Considerations

1. **Caching**: Cache tenant configurations
2. **Indexing**: Proper database indexes for tenant queries
3. **Connection Pooling**: Tenant-aware connection management
4. **Lazy Loading**: Load configurations on demand

## Next Steps

1. Start with Phase 1 - implement core tenant models and context
2. Set up tenant resolution and basic multi-tenancy
3. Migrate existing data to be tenant-aware
4. Implement configuration management
5. Build dynamic form engine
6. Update frontend to be configuration-driven
7. Implement workflow engine
8. Add comprehensive testing
9. Performance optimization
10. Documentation and deployment

## Sample Client Configurations

Reference the sample configurations in:
- `database/config-samples/client-a-config.json` - Tech company with development focus
- `database/config-samples/client-b-config.json` - Manufacturing company with shift work

This implementation will transform your single-tenant application into a flexible, multi-tenant SaaS platform that can accommodate diverse client requirements through configuration rather than code changes.

## Key Benefits

1. **Scalability**: Support unlimited clients with isolated data
2. **Customization**: Each client gets tailored fields and workflows
3. **Maintainability**: Single codebase serves all clients
4. **Revenue Growth**: SaaS model with multiple paying clients
5. **Competitive Advantage**: Highly configurable solution

Start with the database schema implementation and work through each phase systematically. The modular approach ensures you can deploy and test each component independently.