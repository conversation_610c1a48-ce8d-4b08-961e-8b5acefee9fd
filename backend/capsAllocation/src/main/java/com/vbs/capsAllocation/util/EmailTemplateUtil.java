package com.vbs.capsAllocation.util;

import org.springframework.stereotype.Component;

@Component
public class EmailTemplateUtil {

    public String getVunnoApprovalEmail(String backupInfo) {
        return "<!DOCTYPE html>\n"
               + "<html>\n"
               + "<head>\n"
               + "  <base target=\"_top\">\n"
               + "</head>\n"
               + "<body>\n"
               + "  <p>Please, reach out to the aligned backup in their absence.</p>\n"
                + "<p>BackupInfo: " + backupInfo + "</p>\n"
               + "  <p><b>Feel free to contact Leads in case you don't find any secondary aligned to their tasks.</b></p>\n"
               + "</body>\n"
               + "</html>";
    }

    public String getVunnoDeclineMail(){
        return "<!DOCTYPE html>\n"
                + "<html>\n"
                + "<head>\n"
                + "  <base target=\"_top\">\n"
                + "</head>\n"
                + "<body>\n"
                + "  <p><b>Your Request has been Declined.</b></p>\n"
                + "  <p>Please,feel free to contact your approver.</p>\n"
                + "</body>\n"
                + "</html>";
    }

}
