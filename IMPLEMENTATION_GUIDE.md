# TeamSphere Multi-Tenant Implementation Guide

## Overview
This guide provides step-by-step instructions for transforming the existing TeamSphere time-sheet portal into a multi-tenant SaaS platform with customizable fields, workflows, and business rules.

## Phase 1: Database Schema Implementation

### Step 1: Execute Database Schema
```bash
# Run the multi-tenant schema
psql -U postgres -d vbs_allocation_caps -f database/multi-tenant-schema.sql

# Run the migration script
psql -U postgres -d vbs_allocation_caps -f database/tenant-migration.sql
```

### Step 2: Verify Schema Changes
```sql
-- Verify tenant tables exist
SELECT table_name FROM information_schema.tables
WHERE table_schema = 'public' AND table_name LIKE 'tenant%';

-- Verify tenant_id columns added
SELECT column_name, data_type FROM information_schema.columns
WHERE table_name = 'time_entries' AND column_name = 'tenant_id';
```

## Phase 2: Backend Implementation

### Step 1: Create Tenant Management Models

#### 1.1 Tenant Entity
Create: `backend/capsAllocation/src/main/java/com/vbs/capsAllocation/model/Tenant.java`

```java
@Entity
@Table(name = "tenants")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Tenant {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = true, nullable = false)
    private String tenantCode;

    @Column(nullable = false)
    private String tenantName;

    private String domain;
    private String subdomain;

    @Enumerated(EnumType.STRING)
    private TenantStatus status = TenantStatus.ACTIVE;

    private String subscriptionPlan;
    private Integer maxUsers;
    private Integer storageLimitGb;

    @CreationTimestamp
    private LocalDateTime createdAt;

    @UpdateTimestamp
    private LocalDateTime updatedAt;

    private String createdBy;
    private String contactEmail;
    private String contactPhone;
    private String address;
    private String timezone;
    private String dateFormat;
    private String timeFormat;
}
```

#### 1.2 Supporting Enums
Create: `backend/capsAllocation/src/main/java/com/vbs/capsAllocation/model/TenantStatus.java`

```java
public enum TenantStatus {
    ACTIVE, INACTIVE, SUSPENDED
}
```

Create: `backend/capsAllocation/src/main/java/com/vbs/capsAllocation/model/ConfigType.java`

```java
public enum ConfigType {
    STRING, JSON, BOOLEAN, NUMBER, ARRAY
}
```

#### 1.3 Tenant Configuration Entity
Create: `backend/capsAllocation/src/main/java/com/vbs/capsAllocation/model/TenantConfiguration.java`

```java
@Entity
@Table(name = "tenant_configurations")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TenantConfiguration {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "tenant_id", nullable = false)
    private Tenant tenant;

    @Column(nullable = false)
    private String configCategory;

    @Column(nullable = false)
    private String configKey;

    @Column(columnDefinition = "TEXT")
    private String configValue;

    @Enumerated(EnumType.STRING)
    private ConfigType configType = ConfigType.STRING;

    private String description;
    private Boolean isEncrypted = false;

    @CreationTimestamp
    private LocalDateTime createdAt;

    @UpdateTimestamp
    private LocalDateTime updatedAt;
}
```

### Step 2: Create Tenant Context Service

#### 2.1 Tenant Context Service
Create: `backend/capsAllocation/src/main/java/com/vbs/capsAllocation/service/TenantContextService.java`

```java
@Service
@Slf4j
public class TenantContextService {

    private static final ThreadLocal<Long> currentTenantId = new ThreadLocal<>();

    public void setCurrentTenant(Long tenantId) {
        currentTenantId.set(tenantId);
        log.debug("Set current tenant to: {}", tenantId);
    }

    public Long getCurrentTenantId() {
        return currentTenantId.get();
    }

    public void clear() {
        currentTenantId.remove();
    }

    public boolean hasTenantContext() {
        return currentTenantId.get() != null;
    }
}
```

#### 2.2 Tenant Resolution Filter
Create: `backend/capsAllocation/src/main/java/com/vbs/capsAllocation/filter/TenantResolutionFilter.java`

```java
@Component
@Slf4j
public class TenantResolutionFilter implements Filter {

    @Autowired
    private TenantService tenantService;

    @Autowired
    private TenantContextService tenantContextService;

    @Override
    public void doFilter(ServletRequest request, ServletResponse response,
                        FilterChain chain) throws IOException, ServletException {

        HttpServletRequest httpRequest = (HttpServletRequest) request;

        try {
            // Resolve tenant from subdomain or header
            String tenantIdentifier = resolveTenantIdentifier(httpRequest);

            if (tenantIdentifier != null) {
                Tenant tenant = tenantService.findByCodeOrSubdomain(tenantIdentifier);
                if (tenant != null && tenant.getStatus() == TenantStatus.ACTIVE) {
                    tenantContextService.setCurrentTenant(tenant.getId());
                }
            }

            chain.doFilter(request, response);

        } finally {
            tenantContextService.clear();
        }
    }

    private String resolveTenantIdentifier(HttpServletRequest request) {
        // Try header first (for API calls)
        String tenantHeader = request.getHeader("X-Tenant-ID");
        if (tenantHeader != null) {
            return tenantHeader;
        }

        // Try subdomain
        String serverName = request.getServerName();
        if (serverName != null && serverName.contains(".")) {
            String[] parts = serverName.split("\\.");
            if (parts.length > 2) {
                return parts[0]; // subdomain
            }
        }

        return "DEFAULT"; // fallback to default tenant
    }
}
```

### Step 3: Create Repositories

#### 3.1 Tenant Repository
Create: `backend/capsAllocation/src/main/java/com/vbs/capsAllocation/repository/TenantRepository.java`

```java
@Repository
public interface TenantRepository extends JpaRepository<Tenant, Long> {

    Optional<Tenant> findByTenantCode(String tenantCode);

    Optional<Tenant> findBySubdomain(String subdomain);

    Optional<Tenant> findByDomain(String domain);

    @Query("SELECT t FROM Tenant t WHERE t.tenantCode = :identifier OR t.subdomain = :identifier")
    Optional<Tenant> findByCodeOrSubdomain(@Param("identifier") String identifier);

    List<Tenant> findByStatus(TenantStatus status);

    @Query("SELECT COUNT(t) FROM Tenant t WHERE t.status = 'ACTIVE'")
    Long countActiveTenants();
}
```

#### 3.2 Tenant Configuration Repository
Create: `backend/capsAllocation/src/main/java/com/vbs/capsAllocation/repository/TenantConfigurationRepository.java`

```java
@Repository
public interface TenantConfigurationRepository extends JpaRepository<TenantConfiguration, Long> {

    List<TenantConfiguration> findByTenantId(Long tenantId);

    List<TenantConfiguration> findByTenantIdAndConfigCategory(Long tenantId, String configCategory);

    Optional<TenantConfiguration> findByTenantIdAndConfigCategoryAndConfigKey(
        Long tenantId, String configCategory, String configKey);

    @Query("SELECT tc FROM TenantConfiguration tc WHERE tc.tenant.id = :tenantId AND tc.configKey IN :keys")
    List<TenantConfiguration> findByTenantIdAndConfigKeys(
        @Param("tenantId") Long tenantId, @Param("keys") List<String> keys);
}
```