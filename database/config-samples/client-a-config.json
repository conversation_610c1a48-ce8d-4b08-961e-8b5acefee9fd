{"tenantInfo": {"tenantCode": "CLIENT_A", "tenantName": "Tech Solutions Inc", "domain": "techsolutions.com", "subdomain": "timesheet", "timezone": "America/New_York", "dateFormat": "MM/DD/YYYY", "timeFormat": "12H"}, "timeEntryConfig": {"maxDailyHours": 10, "overtimeThreshold": 8, "allowWeekendEntries": true, "allowFutureEntries": false, "maxFutureDays": 0, "allowPastEntries": true, "maxPastDays": 30, "minimumTimeIncrement": 15, "roundingRule": "NEAREST_QUARTER"}, "customFields": [{"entityType": "TIME_ENTRY", "fieldName": "cost_center", "fieldLabel": "Cost Center", "fieldType": "SELECT", "isRequired": true, "displayOrder": 1, "fieldOptions": [{"value": "CC001", "label": "Development"}, {"value": "CC002", "label": "QA Testing"}, {"value": "CC003", "label": "DevOps"}, {"value": "CC004", "label": "Support"}]}, {"entityType": "TIME_ENTRY", "fieldName": "client_reference", "fieldLabel": "Client Reference Number", "fieldType": "TEXT", "isRequired": false, "displayOrder": 2, "validationRules": {"pattern": "^[A-Z]{2}-\\d{4}$", "message": "Format: XX-1234"}}, {"entityType": "TIME_ENTRY", "fieldName": "billable_flag", "fieldLabel": "Billable to Client", "fieldType": "BOOLEAN", "isRequired": true, "displayOrder": 3, "defaultValue": "true"}, {"entityType": "PROJECT", "fieldName": "project_manager", "fieldLabel": "Project Manager", "fieldType": "TEXT", "isRequired": true, "displayOrder": 1}], "activities": [{"activityCode": "DEVELOPMENT", "activityName": "Software Development", "category": "PRODUCTIVE", "isBillable": true, "isOvertimeEligible": true, "colorCode": "#4CAF50"}, {"activityCode": "CODE_REVIEW", "activityName": "Code Review", "category": "PRODUCTIVE", "isBillable": true, "isOvertimeEligible": true, "colorCode": "#2196F3"}, {"activityCode": "TESTING", "activityName": "Testing & QA", "category": "PRODUCTIVE", "isBillable": true, "isOvertimeEligible": true, "colorCode": "#FF9800"}, {"activityCode": "DOCUMENTATION", "activityName": "Documentation", "category": "PRODUCTIVE", "isBillable": true, "isOvertimeEligible": false, "colorCode": "#9C27B0"}, {"activityCode": "TRAINING", "activityName": "Training & Learning", "category": "NON_PRODUCTIVE", "isBillable": false, "isOvertimeEligible": false, "colorCode": "#607D8B"}], "attendanceTypes": [{"attendanceCode": "FULL_DAY", "attendanceName": "Full Day", "isFullDay": true, "multiplier": 1.0}, {"attendanceCode": "HALF_DAY", "attendanceName": "Half Day", "isFullDay": false, "multiplier": 0.5}, {"attendanceCode": "OVERTIME", "attendanceName": "Overtime", "isFullDay": false, "multiplier": 1.5}], "workflowConfig": {"approvalWorkflows": [{"workflowName": "Standard Approval", "workflowType": "TIME_ENTRY", "isDefault": true, "steps": [{"step": 1, "role": "LEAD", "action": "APPROVE_OR_REJECT", "escalationHours": 24, "autoApprovalConditions": {"maxHours": 8, "billableOnly": false}}, {"step": 2, "role": "MANAGER", "action": "APPROVE_OR_REJECT", "escalationHours": 48, "activationConditions": {"overtimeHours": ">8", "weeklyHours": ">40"}}]}]}, "businessRules": [{"ruleName": "Overtime Validation", "ruleType": "VALIDATION", "entityType": "TIME_ENTRY", "conditions": {"timeInMins": ">480"}, "actions": {"requireApproval": true, "notifyManager": true}}, {"ruleName": "Weekend Entry Notification", "ruleType": "NOTIFICATION", "entityType": "TIME_ENTRY", "conditions": {"dayOfWeek": ["SATURDAY", "SUNDAY"]}, "actions": {"sendEmail": true, "emailTemplate": "weekend_entry_notification"}}], "uiConfig": {"themeColor": "#1976d2", "logoUrl": "/assets/client-a-logo.png", "companyName": "Tech Solutions Inc", "showBranding": true, "customCss": "", "hiddenFeatures": [], "defaultView": "TIME_ENTRY"}, "notificationConfig": {"emailEnabled": true, "smsEnabled": false, "approvalReminders": {"enabled": true, "intervalHours": 24, "maxReminders": 3}, "submissionReminders": {"enabled": true, "dayOfWeek": "FRIDAY", "time": "16:00"}}}