{"tenantInfo": {"tenantCode": "CLIENT_B", "tenantName": "Global Manufacturing Corp", "domain": "globalmanufacturing.com", "subdomain": "hrs", "timezone": "Europe/London", "dateFormat": "DD/MM/YYYY", "timeFormat": "24H"}, "timeEntryConfig": {"maxDailyHours": 12, "overtimeThreshold": 8, "allowWeekendEntries": false, "allowFutureEntries": true, "maxFutureDays": 7, "allowPastEntries": true, "maxPastDays": 14, "minimumTimeIncrement": 30, "roundingRule": "ROUND_UP"}, "customFields": [{"entityType": "TIME_ENTRY", "fieldName": "shift_type", "fieldLabel": "Shift Type", "fieldType": "SELECT", "isRequired": true, "displayOrder": 1, "fieldOptions": [{"value": "DAY", "label": "Day Shift (06:00-14:00)"}, {"value": "EVENING", "label": "Evening Shift (14:00-22:00)"}, {"value": "NIGHT", "label": "Night Shift (22:00-06:00)"}]}, {"entityType": "TIME_ENTRY", "fieldName": "machine_id", "fieldLabel": "Machine/Station ID", "fieldType": "TEXT", "isRequired": true, "displayOrder": 2, "validationRules": {"pattern": "^M\\d{3}$", "message": "Format: M001"}}, {"entityType": "TIME_ENTRY", "fieldName": "production_units", "fieldLabel": "Units Produced", "fieldType": "NUMBER", "isRequired": false, "displayOrder": 3, "validationRules": {"min": 0, "max": 10000}}, {"entityType": "TIME_ENTRY", "fieldName": "safety_incident", "fieldLabel": "Safety Incident Reported", "fieldType": "BOOLEAN", "isRequired": true, "displayOrder": 4, "defaultValue": "false"}, {"entityType": "PROJECT", "fieldName": "production_line", "fieldLabel": "Production Line", "fieldType": "SELECT", "isRequired": true, "displayOrder": 1, "fieldOptions": [{"value": "LINE_A", "label": "Production Line A"}, {"value": "LINE_B", "label": "Production Line B"}, {"value": "LINE_C", "label": "Production Line C"}]}], "activities": [{"activityCode": "PRODUCTION", "activityName": "Production Work", "category": "PRODUCTIVE", "isBillable": true, "isOvertimeEligible": true, "colorCode": "#4CAF50"}, {"activityCode": "MAINTENANCE", "activityName": "Equipment Maintenance", "category": "PRODUCTIVE", "isBillable": true, "isOvertimeEligible": true, "colorCode": "#FF9800"}, {"activityCode": "QUALITY_CHECK", "activityName": "Quality Control", "category": "PRODUCTIVE", "isBillable": true, "isOvertimeEligible": true, "colorCode": "#2196F3"}, {"activityCode": "SETUP", "activityName": "Machine Setup", "category": "PRODUCTIVE", "isBillable": true, "isOvertimeEligible": false, "colorCode": "#9C27B0"}, {"activityCode": "BREAK", "activityName": "Break Time", "category": "NON_PRODUCTIVE", "isBillable": false, "isOvertimeEligible": false, "colorCode": "#607D8B"}, {"activityCode": "SAFETY_TRAINING", "activityName": "Safety Training", "category": "ADMINISTRATIVE", "isBillable": false, "isOvertimeEligible": false, "colorCode": "#F44336"}], "attendanceTypes": [{"attendanceCode": "DAY_SHIFT", "attendanceName": "Day Shift", "isFullDay": true, "multiplier": 1.0}, {"attendanceCode": "EVENING_SHIFT", "attendanceName": "Evening Shift", "isFullDay": true, "multiplier": 1.1}, {"attendanceCode": "NIGHT_SHIFT", "attendanceName": "Night Shift", "isFullDay": true, "multiplier": 1.2}, {"attendanceCode": "DOUBLE_SHIFT", "attendanceName": "Double Shift", "isFullDay": false, "multiplier": 2.0}], "workflowConfig": {"approvalWorkflows": [{"workflowName": "Manufacturing Approval", "workflowType": "TIME_ENTRY", "isDefault": true, "steps": [{"step": 1, "role": "LEAD", "action": "APPROVE_OR_REJECT", "escalationHours": 12, "autoApprovalConditions": {"maxHours": 8, "noSafetyIncidents": true}}, {"step": 2, "role": "MANAGER", "action": "APPROVE_OR_REJECT", "escalationHours": 24, "activationConditions": {"overtimeHours": ">8", "safetyIncident": true}}]}]}, "businessRules": [{"ruleName": "Safety Incident Escalation", "ruleType": "AUTO_ACTION", "entityType": "TIME_ENTRY", "conditions": {"customFields.safety_incident": true}, "actions": {"escalateToManager": true, "sendSafetyAlert": true, "requireManagerApproval": true}}, {"ruleName": "Night Shift Premium", "ruleType": "CALCULATION", "entityType": "TIME_ENTRY", "conditions": {"customFields.shift_type": "NIGHT"}, "actions": {"applyMultiplier": 1.2, "addPremiumFlag": true}}], "uiConfig": {"themeColor": "#FF5722", "logoUrl": "/assets/client-b-logo.png", "companyName": "Global Manufacturing Corp", "showBranding": true, "customCss": ".production-theme { background: #f5f5f5; }", "hiddenFeatures": ["LEAVE_MANAGEMENT"], "defaultView": "TIME_ENTRY"}, "notificationConfig": {"emailEnabled": true, "smsEnabled": true, "approvalReminders": {"enabled": true, "intervalHours": 12, "maxReminders": 5}, "submissionReminders": {"enabled": false}, "safetyAlerts": {"enabled": true, "immediateNotification": true, "recipients": ["<EMAIL>"]}}}